# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Test

- `mvn clean compile` - Compile the project
- `mvn test` - Run unit tests
- `mvn integration-test` - Run integration tests
- `mvn package` - Build the complete project
- `mvn clean package -Pdeployable-assembly` - Build deployable assembly

### Frontend Development (GUI module)

- `cd gui/src/main/web/`
- `yarn install` - Install dependencies
- `yarn start` - Start development server on port 8082
- `yarn build` - Build production assets
- `yarn lint` - Run TSLint

### Running the Application

- Default port: 12590
- Spring profiles: `local_local_local` (local development), `de_component-test_eu-west-1` (CI/CD)

## Architecture Overview

This is a Spring Boot microservice that implements a vehicle ping service with multi-version protocol support (V1 and V2). The service handles bidirectional
communication between vehicles and backend systems.

### Key Components

#### Core Domain Logic

- **PingManager** (`impl/src/main/java/.../domain/PingManager.java`) - Central orchestrator for all ping operations
- **Version-specific handlers** in `domain/v1/` and `domain/v2/` packages
- **Database entities** in `database/src/main/java/.../entity/`

#### Multi-Module Structure

- **impl/** - Main business logic and Spring Boot configuration
- **database/** - MongoDB entities and repositories (reactive)
- **gui/** - Web UI with React/TypeScript frontend
- **api-openapi-impl/** - OpenAPI implementation
- **swap-asn1/** - ASN.1 protocol definitions and generated code
- **swap-json/** - JSON schema definitions
- **integration-tests/** - Full integration test suite

#### External Dependencies

- **MongoDB** - Reactive data persistence
- **Apache Artemis** - JMS messaging
- **State Repository** - Temporary state management with timeouts
- **Telematic Unit Service (TUS)** - Vehicle version lookup
- **MT Dispatcher** - Message routing to vehicles
- **Identity Provider M2M** - Authentication/authorization

#### Protocol Support

- **V1 Protocol** - ASN.1 encoded messages, separate ping/beefy endpoints
- **V2 Protocol** - JSON with optional ZSTD compression
- **Compression** - ZSTD compression handler for V2 messages

#### Key Features

- Bidirectional ping/pong communication
- Payload support with optional copying
- Message correlation and state tracking
- Comprehensive metrics and monitoring
- Multi-version protocol support
- JWT token handling and service access tokens

### Database Schema

- **PingEntity** - Tracks ping requests with correlation IDs, status, and metadata
- **MessageIdRepo** - Manages message ID sequences
- Reactive MongoDB repositories for async operations

### Configuration

- **application.yaml** - Environment-specific configurations
- **AppConfig.java** - Main Spring configuration with bean definitions
- Service discovery endpoints configurable per environment

### Testing Strategy

- Unit tests for each module
- Integration tests with Testcontainers
- Mocked external dependencies for testing
- Wiremock for HTTP service mocking

### Custom

- Metrics queries use days variable