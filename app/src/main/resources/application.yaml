server:
  port: 12590

---
## CI/CD (Delivery Engine) specific
spring.config.activate.on-profile: de_component-test_eu-west-1

management:
  influx:
    metrics:
      export:
        enabled: false
  tracing:
    enabled: false

servicediscovery:
  auth: http://mockhost:8080
  subr: http://mockhost:8080
  state: http://mockhost:8080
  tus: http://mockhost:8080
  vil: http://mockhost:8080
  idpm2m: http://mockhost:8080

spring:
  artemis:
    broker-url: tcp://mockhost:61616
  data:
    mongodb:
      uri: mongodb://mockhost:27017/DE_EUWEST1_COMPONENTTEST_VPS

oauth2:
  idpm2m:
    secret: Y2xpZW50SWQ6Y2xpZW50U2VjcmV0

---
## local specific properties
spring.config.activate.on-profile: local_local_local

management:
  influx:
    metrics:
      export:
        enabled: false
  tracing:
    enabled: false

servicediscovery:
  auth: http://localhost:8080
  subr: http://localhost:8080
  state: http://localhost:8080
  tus: http://localhost:8080
  vil: http://localhost:8080
  idpm2m: http://localhost:8080

spring:
  artemis:
    broker-url: tcp://localhost:61616
  data:
    mongodb:
      uri: mongodb://localhost:27017/LOCAL_LOCAL_LOCAL_VPS

oauth2:
  idpm2m:
    secret: Y2xpZW50SWQ6Y2xpZW50U2VjcmV0
