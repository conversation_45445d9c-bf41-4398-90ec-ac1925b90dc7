<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vehicle-ping-service-server-app</artifactId>
  <packaging>jar</packaging>
  <name>Vehicle Ping Service :: Server :: Application</name>
  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencies>
    <!-- Our own GUI -->
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-gui</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-impl</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- Configuration -->
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-property</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-zookeeper</artifactId>
      <scope>runtime</scope>
    </dependency>
  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Build plug-ins -->
  <!-- +=============================================== -->
  <build>
    <plugins>
      <plugin>
        <!-- spring-boot plugin enables: mvn spring-boot:run -->
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass>com.volvo.tisp.vehiclepingservice.conf.AppConfig</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
